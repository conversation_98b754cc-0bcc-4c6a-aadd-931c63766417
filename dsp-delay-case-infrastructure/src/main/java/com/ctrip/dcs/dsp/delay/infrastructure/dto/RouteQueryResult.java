package com.ctrip.dcs.dsp.delay.infrastructure.dto;

import com.ctrip.dcs.dsp.delay.model.Route;

public class RouteQueryResult {
    private final Route gaodeRoute;
    private final Route baiduRoute;

    public RouteQueryResult(Route gaodeRoute, Route baiduRoute) {
        this.gaodeRoute = gaodeRoute;
        this.baiduRoute = baiduRoute;
    }

    public Route getGaodeRoute() { return gaodeRoute; }
    public Route getBaiduRoute() { return baiduRoute; }
}
