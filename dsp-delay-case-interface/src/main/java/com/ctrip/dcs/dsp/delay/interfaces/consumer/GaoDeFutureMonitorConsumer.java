package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureMonitorDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.util.DoubleUtils;
import com.ctrip.dcs.dsp.delay.infrastructure.util.MonitorUtil;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;

import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 高德未来路径监控延迟消息消费者
 * 在真实出发时间点调用实时预估路径接口，获取真实的预估路径时长距离
 * 
 * <AUTHOR>
 */
@Component
public class GaoDeFutureMonitorConsumer {

    private static final Logger logger = LoggerFactory.getLogger(GaoDeFutureMonitorConsumer.class);

    @Autowired
    private GeoGateway geoGateway;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.GAODE_FUTURE_MONITOR_DELAY_SUBJECT, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        try {
            // 从消息中获取数据
            String gaoDeFutureMonitorDTOJson = message.getStringProperty("data");
            // 反序列化gaoDeFutureMonitorDTO
            GaoDeFutureDTO gaoDeFutureDTO = JsonUtil.fromJson(gaoDeFutureMonitorDTOJson, new TypeReference<GaoDeFutureDTO>() {});
            if (gaoDeFutureDTO == null) {
                logger.warn("GaoDeFutureMonitorConsumer.onMessage", "gaoDeFutureMonitorDTO error");
                return;
            }

            Integer cityId = gaoDeFutureDTO.getCityId();
            String userOrderId = gaoDeFutureDTO.getUserOrderId();

            // 构建Position对象
            Position position = new Position();
            position.setFromLongitude(gaoDeFutureDTO.getFromLongitude());
            position.setFromCoordsys(gaoDeFutureDTO.getFromCoordsys());
            position.setFromLatitude(gaoDeFutureDTO.getFromLatitude());
            position.setToLongitude(gaoDeFutureDTO.getToLongitude());
            position.setToLatitude(gaoDeFutureDTO.getToLatitude());
            position.setToCoordsys(gaoDeFutureDTO.getToCoordsys());

            // 调用实时预估路径接口获取真实的预估路径时长距离
            List<Route> actualRoutes = geoGateway.queryRoutes(cityId, Collections.singletonList(position));

            if (actualRoutes != null && !actualRoutes.isEmpty()) {
                Route actualRoute = actualRoutes.get(0);

                // 更新gaoDeFutureMonitorDTO的实际数据
                // 计算差值
                GaoDeFutureMonitorDTO gaoDeFutureMonitorDTO = new GaoDeFutureMonitorDTO();
                try {
                    gaoDeFutureMonitorDTO.setCityId(gaoDeFutureDTO.getCityId().toString());
                    gaoDeFutureMonitorDTO.setOrderId(gaoDeFutureDTO.getUserOrderId());
                    gaoDeFutureMonitorDTO.setDepartureTime(DateUtil.formatDate(gaoDeFutureDTO.getDepartureTime(), DateUtil.DATE_FMT));
                    gaoDeFutureMonitorDTO.setOrigin(GeoHashUtil.buildGeoHash(gaoDeFutureDTO.getFromLongitude(), gaoDeFutureDTO.getFromLatitude()));
                    gaoDeFutureMonitorDTO.setDestination(GeoHashUtil.buildGeoHash(gaoDeFutureDTO.getToLongitude(), gaoDeFutureDTO.getToLatitude()));
                    gaoDeFutureMonitorDTO.setRealTimeDistance(DoubleUtils.toString(gaoDeFutureDTO.getRealTimeDistance()));
                    gaoDeFutureMonitorDTO.setRealTimeDuration(DoubleUtils.toString(gaoDeFutureDTO.getRealTimeDuration()));
                    gaoDeFutureMonitorDTO.setFutureDistance(DoubleUtils.toString(gaoDeFutureDTO.getFutureDistance()));
                    gaoDeFutureMonitorDTO.setFutureDuration(DoubleUtils.toString(gaoDeFutureDTO.getFutureDuration()));
                    gaoDeFutureMonitorDTO.setExecuteTime(DateUtil.formatDate(gaoDeFutureDTO.getExecuteTime(), DateUtil.DATE_FMT));
                    gaoDeFutureMonitorDTO.setActualDistance(DoubleUtils.toString(actualRoute.getDistance()));
                    gaoDeFutureMonitorDTO.setActualDuration(DoubleUtils.toString(actualRoute.getDuration()));
                    gaoDeFutureMonitorDTO.setRealTimeDiffDuration(DoubleUtils.toString(gaoDeFutureDTO.getRealTimeDuration() - gaoDeFutureDTO.getFutureDuration()));
                    gaoDeFutureMonitorDTO.setActualDiffDuration(DoubleUtils.toString(actualRoute.getDuration() - gaoDeFutureDTO.getFutureDuration()));
                    gaoDeFutureMonitorDTO.setBaiDuFutureDuration(DoubleUtils.toString(gaoDeFutureDTO.getBaiDuFutureDuration()));
                    gaoDeFutureMonitorDTO.setBaiDuFutureDistance(DoubleUtils.toString(gaoDeFutureDTO.getBaiDuFutureDistance()));
                    gaoDeFutureMonitorDTO.setBaiDuGaoDeFutureTimeDiff(Objects.isNull(gaoDeFutureDTO.getBaiDuFutureDuration()) ? null : DoubleUtils.toString(gaoDeFutureDTO.getBaiDuFutureDuration() - gaoDeFutureDTO.getFutureDuration()));
                    gaoDeFutureMonitorDTO.setScene("delay-gaode-future-monitor");
                } catch (NumberFormatException e) {
                    logger.warn("GaoDeFutureMonitorConsumer.onMessage",  e);
                }
                // 调用监控工具记录最终数据
                MonitorUtil.monitor(gaoDeFutureMonitorDTO);

                logger.info("GaoDeFutureMonitorConsumer.onMessage", "process Success, orderId:{}, actualDistance:{}, actualDuration:{}", userOrderId, gaoDeFutureMonitorDTO.getActualDistance(),
                    gaoDeFutureMonitorDTO.getActualDuration());
            } else {
                logger.warn("GaoDeFutureMonitorConsumer.onMessage", " prcess realTimeRoute fail, orderId:{}", userOrderId);
            }

        } catch (Exception e) {
            logger.error("GaoDeFutureMonitorConsumer.onMessage error",  e);
        }
    }
}
